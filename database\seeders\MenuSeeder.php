<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Menu;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $menus = [
            ['name' => 'About', 'status' => 'Show'],
            ['name' => 'Services', 'status' => 'Show'],
            ['name' => 'Portfolios', 'status' => 'Show'],
            ['name' => 'Team Members', 'status' => 'Show'],
            ['name' => 'Testimonials', 'status' => 'Show'],
            ['name' => 'FAQ', 'status' => 'Show'],
            ['name' => 'Blog', 'status' => 'Show'],
            ['name' => 'Contact', 'status' => 'Show'],
            ['name' => 'Pricing', 'status' => 'Show'],
            ['name' => 'Terms of Use', 'status' => 'Show'],
            ['name' => 'Privacy Policy', 'status' => 'Show']
        ];

        foreach ($menus as $menu) {
            if (!Menu::where('name', $menu['name'])->exists()) {
                Menu::create($menu);
            }
        }

        echo "تم إنشاء عناصر القائمة بنجاح!\n";
    }
}
