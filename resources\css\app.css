@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    body {
        direction: rtl;
        font-family: 'Cairo', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Almarai', sans-serif;
    }

    * {
        font-family: 'Cairo', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Almarai', sans-serif;
        direction: rtl;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: 'Cairo', 'Changa', sans-serif;
        font-weight: 600;
        direction: rtl;
        text-align: right;
    }

    /* RTL Improvements */
    .container, .container-fluid {
        direction: rtl;
    }

    .text-left {
        text-align: right !important;
    }

    .text-right {
        text-align: left !important;
    }

    .float-left {
        float: right !important;
    }

    .float-right {
        float: left !important;
    }
}

/* <PERSON><PERSON>sent <PERSON>yling */
.wpcc-container {
    font-family: 'Cairo', 'Changa', 'Taja<PERSON>', sans-serif !important;
    direction: rtl !important;
    text-align: right !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    max-width: 420px !important;
    min-height: 140px !important;
    margin: 20px !important;
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 9999 !important;
    overflow: visible !important;
    padding: 20px 20px 15px 20px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    align-items: stretch !important;
}

.wpcc-container::before {
    content: "🍪" !important;
    position: absolute !important;
    top: 15px !important;
    right: 15px !important;
    font-size: 24px !important;
    opacity: 0.8 !important;
}

.wpcc-message {
    font-size: 14px !important;
    line-height: 1.7 !important;
    margin-bottom: 20px !important;
    font-weight: 400 !important;
    padding-right: 50px !important;
    margin-top: 10px !important;
    padding-bottom: 10px !important;
    clear: both !important;
    display: block !important;
    width: 100% !important;
}

.wpcc-btn {
    border-radius: 8px !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 12px rgba(254, 198, 63, 0.3) !important;
    display: block !important;
    width: auto !important;
    margin-top: 1px !important;
    margin-bottom: 5px !important;
    clear: both !important;
    float: none !important;
    text-align: center !important;
    align-self: flex-end !important;
}

.wpcc-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(254, 198, 63, 0.4) !important;
}

/* Animation for cookie banner */
@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.wpcc-container {
    animation: slideInUp 0.5s ease-out !important;
}

/* Ensure cookie banner stays at bottom */
body .wpcc-container,
html .wpcc-container {
    position: fixed !important;
    bottom: 20px !important;
    top: auto !important;
    transform: none !important;
}

/* Text styling for mixed content */
.wpcc-message,
.wpcc-container * {
    word-wrap: break-word !important;
    word-break: break-word !important;
    hyphens: auto !important;
    text-rendering: optimizeLegibility !important;
}

/* Hide "Powered by" text */
.wpcc-container a[href*="websitepolicies"],
.wpcc-container *[class*="powered"],
.wpcc-container *[id*="powered"],
.wpcc-container small,
.wpcc-container .wpcc-footer {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .wpcc-container {
        max-width: calc(100% - 20px) !important;
        bottom: 10px !important;
        right: 10px !important;
        left: 10px !important;
        margin: 0 !important;
        padding: 15px !important;
    }

    .wpcc-message {
        font-size: 13px !important;
        padding-right: 40px !important;
    }

    .wpcc-btn {
        padding: 10px 20px !important;
        font-size: 13px !important;
    }

    .wpcc-container::before {
        font-size: 20px !important;
        top: 12px !important;
        right: 12px !important;
    }
}

/* Force bottom position for all cookie consent variations */
div[class*="wpcc"],
div[id*="cookie"],
div[class*="cookie"] {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    top: auto !important;
}

/* Override any top positioning */
.wpcc-container,
.wpcc-popup {
    top: auto !important;
    bottom: 20px !important;
}

/* WhatsApp Float Button */
.whatsapp-float {
  position: fixed;
  width: 60px;
  height: 60px;
  bottom: 100px;
  right: 30px;
  background-color: #25d366;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  transition: transform 0.3s ease, background-color 0.3s ease;
  animation: float 2s infinite ease-in-out;
}

.whatsapp-float:hover {
  background-color: #20b456;
  transform: scale(1.1);
  box-shadow: 3px 3px 8px rgba(0, 0, 0, 0.4);
}

.whatsapp-icon {
  font-size: 30px;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Clients Grid */
.clients-grid div {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.clients-grid img {
  height: 100px;
  object-fit: contain;
}

.clients-grid p {
  min-height: 40px;
  margin-top: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  color: #4a5568;
}

