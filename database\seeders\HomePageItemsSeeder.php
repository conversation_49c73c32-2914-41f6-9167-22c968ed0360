<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\HomeOnePageItem;
use App\Models\HomeTwoPageItem;
use App\Models\HomeThreePageItem;
use App\Models\HomeFourPageItem;
use App\Models\HomeContactPhoto;
use App\Models\Offer;
use App\Models\Marquee;

class HomePageItemsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Fix HomeOnePageItem
        if (!HomeOnePageItem::where('id', 1)->exists()) {
            HomeOnePageItem::create([
                'id' => 1,
                'service_on_slider_how_many' => '4',
                'service_on_slider_status' => 'Show',
                'welcome_status' => 'Show',
                'service_heading' => 'خدماتنا المتميزة',
                'service_subheading' => 'ما نقدمه لك',
                'service_how_many' => '6',
                'service_status' => 'Show',
                'video_one_status' => 'Show',
                'fun_fact_status' => 'Show',
                'portfolio_heading' => 'أعمالنا',
                'portfolio_subheading' => 'مشاريعنا المتميزة',
                'portfolio_how_many' => '6',
                'portfolio_status' => 'Show',
                'contact_heading' => 'تواصل معنا',
                'contact_subheading' => 'نحن هنا لمساعدتك',
                'contact_status' => 'Show',
                'blog_heading' => 'مدونتنا',
                'blog_subheading' => 'آخر الأخبار والمقالات',
                'blog_how_many' => '3',
                'blog_status' => 'Show',
                'video_two_status' => 'Show',
                'feature_status' => 'Show',
                'testimonial_heading' => 'آراء العملاء',
                'testimonial_subheading' => 'ماذا يقول عملاؤنا',
                'testimonial_text' => 'نفتخر بثقة عملائنا',
                'testimonial_status' => 'Show',
                'why_choose_status' => 'Show',
                'client_status' => 'Show'
            ]);
        }

        // Fix HomeTwoPageItem
        if (!HomeTwoPageItem::where('id', 1)->exists()) {
            HomeTwoPageItem::create([
                'id' => 1,
                'service_heading' => 'خدماتنا',
                'service_subheading' => 'ما نقدمه',
                'service_how_many' => '6',
                'service_status' => 'Show',
                'marquee_status' => 'Show',
                'welcome_status' => 'Show',
                'portfolio_heading' => 'أعمالنا',
                'portfolio_subheading' => 'مشاريعنا',
                'portfolio_how_many' => '6',
                'portfolio_status' => 'Show',
                'why_choose_status' => 'Show',
                'testimonial_heading' => 'آراء العملاء',
                'testimonial_subheading' => 'ماذا يقول عملاؤنا',
                'testimonial_status' => 'Show',
                'client_status' => 'Show',
                'team_member_heading' => 'فريق العمل',
                'team_member_subheading' => 'تعرف على فريقنا',
                'team_member_how_many' => '4',
                'team_member_status' => 'Show',
                'contact_heading' => 'تواصل معنا',
                'contact_subheading' => 'نحن هنا لمساعدتك',
                'contact_status' => 'Show'
            ]);
        }

        // Fix HomeThreePageItem
        if (!HomeThreePageItem::where('id', 1)->exists()) {
            HomeThreePageItem::create([
                'id' => 1,
                'service_how_many' => '6',
                'service_status' => 'Show',
                'welcome_status' => 'Show',
                'offer_status' => 'Show',
                'portfolio_heading' => 'أعمالنا',
                'portfolio_subheading' => 'مشاريعنا',
                'portfolio_how_many' => '6',
                'portfolio_text' => 'نقدم أفضل الحلول',
                'portfolio_status' => 'Show',
                'video_status' => 'Show',
                'feature_status' => 'Show',
                'call_to_action_status' => 'Show',
                'client_status' => 'Show',
                'team_member_heading' => 'فريق العمل',
                'team_member_subheading' => 'تعرف على فريقنا',
                'team_member_how_many' => '4',
                'team_member_status' => 'Show',
                'contact_heading' => 'تواصل معنا',
                'contact_subheading' => 'نحن هنا لمساعدتك',
                'contact_status' => 'Show'
            ]);
        }

        // Fix HomeFourPageItem
        if (!HomeFourPageItem::where('id', 1)->exists()) {
            HomeFourPageItem::create([
                'id' => 1,
                'service_heading' => 'خدماتنا',
                'service_subheading' => 'ما نقدمه',
                'service_how_many' => '6',
                'service_status' => 'Show',
                'marquee_status' => 'Show',
                'welcome_status' => 'Show',
                'portfolio_heading' => 'أعمالنا',
                'portfolio_subheading' => 'مشاريعنا',
                'portfolio_how_many' => '6',
                'portfolio_status' => 'Show',
                'why_choose_status' => 'Show',
                'testimonial_heading' => 'آراء العملاء',
                'testimonial_subheading' => 'ماذا يقول عملاؤنا',
                'testimonial_status' => 'Show',
                'client_status' => 'Show',
                'team_member_heading' => 'فريق العمل',
                'team_member_subheading' => 'تعرف على فريقنا',
                'team_member_how_many' => '4',
                'team_member_status' => 'Show',
                'contact_heading' => 'تواصل معنا',
                'contact_subheading' => 'نحن هنا لمساعدتك',
                'contact_status' => 'Show'
            ]);
        }

        // Fix HomeContactPhoto
        if (!HomeContactPhoto::where('id', 1)->exists()) {
            HomeContactPhoto::create([
                'id' => 1,
                'home_1_contact_photo' => 'home_1_contact_1705210297.png',
                'home_2_contact_photo' => 'home_2_contact_1705210297.png',
                'home_3_contact_photo' => 'home_3_contact_1705210297.png',
                'home_4_contact_photo' => 'home_4_contact_1705210297.png'
            ]);
        }

        // Fix Offer
        if (!Offer::where('id', 1)->exists()) {
            Offer::create([
                'id' => 1,
                'subheading' => 'عروضنا الخاصة',
                'heading' => 'احصل على خصم خاص',
                'text' => 'عروض محدودة لفترة قصيرة',
                'icon' => 'fas fa-gift',
                'tagline' => 'عرض محدود',
                'youtube_video_id' => 'dQw4w9WgXcQ',
                'photo' => 'offer_photo_1704901634.jpg'
            ]);
        }

        // Fix Marquee items
        if (Marquee::count() == 0) {
            $marqueeItems = [
                'تطوير المواقع الإلكترونية',
                'تطوير التطبيقات',
                'التسويق الرقمي',
                'التصميم الجرافيكي',
                'الاستشارات التقنية'
            ];

            foreach ($marqueeItems as $item) {
                Marquee::create(['item' => $item]);
            }
        }

        echo "تم إنشاء بيانات صفحات الهوم بنجاح!\n";
    }
}
