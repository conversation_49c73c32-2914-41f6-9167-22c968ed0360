<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إضافة الأعمدة المفقودة لجداول الصفحات الرئيسية
        $tables = ['home_one_page_items', 'home_two_page_items', 'home_three_page_items', 'home_four_page_items'];

        foreach ($tables as $tableName) {
            if (Schema::hasTable($tableName)) {
                Schema::table($tableName, function (Blueprint $table) use ($tableName) {
                    if (!Schema::hasColumn($tableName, 'service_on_slider_status')) {
                        $table->string('service_on_slider_status', 50)->default('Show')->nullable();
                    }
                    if (!Schema::hasColumn($tableName, 'service_on_slider_how_many')) {
                        $table->string('service_on_slider_how_many', 10)->default('5')->nullable();
                    }
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $tables = ['home_one_page_items', 'home_two_page_items', 'home_three_page_items', 'home_four_page_items'];

        foreach ($tables as $tableName) {
            if (Schema::hasTable($tableName)) {
                Schema::table($tableName, function (Blueprint $table) {
                    $table->dropColumn(['service_on_slider_status', 'service_on_slider_how_many']);
                });
            }
        }
    }
};
