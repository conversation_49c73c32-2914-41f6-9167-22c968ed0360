@extends('front.layouts.tbl-master')

@section('title', 'TBL Tech - تصميم وبرمجة المواقع الالكترونية')

@section('content')
<!-- Hero Section -->
<section class="bg-gray-50 dark:bg-gray-900">
    <div class="grid max-w-screen-xl px-4 py-8 mx-auto lg:gap-8 xl:gap-0 lg:py-16 lg:grid-cols-12">
        <div class="mr-auto place-self-center lg:col-span-7">
            <h1 class="max-w-2xl mb-4 text-2xl font-extrabold tracking-tight leading-none md:text-5xl xl:text-4xl dark:text-white">
                <div class="relative inline-flex">
                    <h1 class="max-w-2xl mb-4 text-4xl font-extrabold tracking-tight leading-none md:text-5xl xl:text-6xl">
                        تصميم وبرمجة المواقع الالكترونية
                    </h1>
                </div>
            </h1>
            <p class="max-w-2xl mb-6 font-light text-gray-600 lg:mb-8 text-justify md:text-lg lg:text-lg dark:text-gray-400">
                هل تحتاج إلى موقع إلكتروني متميز لعملك أو مشروعك؟ نحن نوفر خدمات تصميم وبرمجة مواقع الإنترنت بجودة عالية وبأسعار تنافسية. فريقنا المؤهل والمتخصص يستخدم أحدث التقنيات والأدوات لتوفير تجربة مستخدم استثنائية، مما يضمن تحويل زيارات المستخدمين إلى عملاء. نحن نهتم بتصميم مواقع الإنترنت الأنيقة والوظيفية والتي تتميز بتصميم متجاوب مع جميع الأجهزة والشاشات. كما نقدم خدمات الدعم الفني المستمر لضمان موقعك يعمل بشكل مثالي ويتوافق مع أحدث المعايير الأمنية. اتصل بنا الآن لتحويل فكرتك إلى واقع ملموس وتجربة متميزة على الإنترنت.
            </p>
        </div>
        <div class="lg:mt-0 sm:mt-6 lg:col-span-5 lg:flex">
            <img src="{{ asset('assets/img/giphy.gif') }}" alt="Web Development" />
        </div>
    </div>
</section>

<!-- Why You Need a Website Section -->
<section class="dark:bg-gray-800 bg-gray-200 dark:text-gray-100">
    <div class="container px-6 py-12 mx-auto">
        <div class="grid items-center gap-4 xl:grid-cols-5">
            <div class="max-w-2xl mx-auto my-8 space-y-4 text-center xl:col-span-2 xl:text-left">
                <h2 class="text-4xl text-right font-bold">لماذا تحتاج لتصميم موقع إلكتروني لأعمالك؟</h2>
                <p class="dark:text-gray-400 text-right">هناك عدة أسباب ومميزات رئيسة للموقع الإلكتروني تجعلك في حاجة لتصميم أعمالك، وهي:</p>
            </div>
            <div class="p-6 xl:col-span-3">
                <div class="grid gap-4 md:grid-cols-2">
                    <div class="grid content-center gap-4">
                        <div class="p-6 rounded shadow-md dark:bg-gray-900 bg-white hover:scale-105 transition-transform">
                            <p>الموقع الإلكتروني يعتبر مقر لأعمالك على الإنترنت، حيث يتم من خلاله توضيح هوية الشركة وخدمتها وجميع المعلومات التي يحتاج العملاء معرفتها عن نشاطك.</p>
                        </div>
                        <div class="p-6 rounded shadow-md dark:bg-gray-900 bg-white hover:scale-105 transition-transform">
                            <p>تكلفته منخفضة نسبيا مقارنة بإنشاء موقع فعلي لنشاطك على أرض الواقع مما يزيد من العائد على الاستثمار.</p>
                        </div>
                        <div class="p-6 rounded shadow-md dark:bg-gray-900 bg-white hover:scale-105 transition-transform">
                            <p>يساهم في جذب العملاء ووصولهم إليك وزيادة الوعي لديهم بخدماتك.</p>
                        </div>
                        <div class="p-6 rounded shadow-md dark:bg-gray-900 bg-white hover:scale-105 transition-transform">
                            <p>يساهم في جذب العملاء ووصولهم إليك وزيادة الوعي لديهم بخدماتك.</p>
                        </div>
                    </div>
                    <div class="grid content-center gap-4">
                        <div class="p-6 rounded shadow-md dark:bg-gray-900 bg-white hover:scale-105 transition-transform">
                            <p>خدمة الشراء عبر الموقع والتعرف على الخدمات ستصبح متاحة 24/7 مما يعني زيادة في المبيعات والأرباح.</p>
                        </div>
                        <div class="p-6 rounded shadow-md dark:bg-gray-900 bg-white hover:scale-105 transition-transform">
                            <p>كلما كان الموقع سريع ومتجاوب مع العملاء كلما زاد رضاهم عن موقعك ويصبح المفضل إليهم.</p>
                        </div>
                        <div class="p-6 rounded shadow-md dark:bg-gray-900 bg-white hover:scale-105 transition-transform">
                            <p>يمكن زيادة عدد زوار الموقع باستخدام الاستراتيجيات التسويقية المختلفة مثل تحسين محركات البحث أو التسويق عبر السوشيال ميديا.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- What Makes Us Different Section -->
<div class="bg-gray-100 dark:bg-gray-900 max-h-full">
    <section class="max-w-8xl mx-auto container pt-16">
        <div>
            <div role="contentinfo" class="flex items-center flex-col px-4">
                <h1 tabindex="0" class="focus:outline-none text-4xl lg:text-4xl font-extrabold text-center leading-10 text-gray-800 dark:text-white lg:w-5/12 md:w-9/12 pt-4">
                    ما يميز تصميم المواقع الالكترونية لدى TBL TECH
                </h1>
            </div>
            <div tabindex="0" aria-label="group of cards" class="focus:outline-none mt-16 flex flex-wrap justify-center gap-5 px-4">
                <div tabindex="0" aria-label="card 1" class="focus:outline-none flex sm:w-full md:w-5/12 pb-20">
                    <div class="w-20 h-20 relative ml-5">
                        <div class="absolute top-0 right-0 bg-indigo-100 rounded w-16 h-16 mt-2 mr-1"></div>
                        <div class="absolute text-white bottom-0 left-0 bg-indigo-700 rounded w-16 h-16 flex items-center justify-center mt-2 mr-3 hover:scale-110 transition-transform">
                            <img src="https://tuk-cdn.s3.amazonaws.com/can-uploader/icon_and_text-SVG1.svg" alt="drawer">
                        </div>
                    </div>
                    <div class="w-10/12">
                        <h2 tabindex="0" class="focus:outline-none text-lg font-bold leading-tight text-gray-800 dark:text-white">
                            متواقف مع محركات البحث
                        </h2>
                        <p tabindex="0" class="focus:outline-none text-base text-gray-600 dark:text-gray-200 leading-normal pt-2">
                            نتبع المعايير القياسية لجوجل لإجراء اعدادات تحسين محركات البحث لموقعك حتى تتمكن من تحسينه لمحركات البحث و الظهور في المراتب الاولى لجوجل
                        </p>
                    </div>
                </div>
                <div tabindex="0" aria-label="card 2" class="focus:outline-none flex sm:w-full md:w-5/12 pb-20">
                    <div class="w-20 h-20 relative ml-5">
                        <div class="absolute top-0 right-0 bg-indigo-100 rounded w-16 h-16 mt-2 mr-1"></div>
                        <div class="absolute text-white bottom-0 left-0 bg-indigo-700 rounded w-16 h-16 flex items-center justify-center mt-2 mr-3 hover:scale-110 transition-transform">
                            <img src="https://tuk-cdn.s3.amazonaws.com/can-uploader/icon_and_text-SVG2.svg" alt="check">
                        </div>
                    </div>
                    <div class="w-10/12">
                        <h2 tabindex="0" class="focus:outline-none text-lg font-semibold leading-tight text-gray-800 dark:text-white">
                            محتوى سهل الإدارة
                        </h2>
                        <p tabindex="0" class="focus:outline-none text-base text-gray-600 dark:text-gray-200 leading-normal pt-2">
                            نقوم بتطوير مواقع باستخدام وردبرس بحيث يمكنك إضافة المحتوى وتحريره وحذفه سهل وسريع
                        </p>
                    </div>
                </div>
                <div tabindex="0" aria-label="card 3" class="focus:outline-none flex sm:w-full md:w-5/12 pb-20">
                    <div class="w-20 h-20 relative ml-5">
                        <div class="absolute top-0 right-0 bg-indigo-100 rounded w-16 h-16 mt-2 mr-1"></div>
                        <div class="absolute text-white bottom-0 left-0 bg-indigo-700 rounded w-16 h-16 flex items-center justify-center mt-2 mr-3 hover:scale-110 transition-transform">
                            <img src="https://tuk-cdn.s3.amazonaws.com/can-uploader/icon_and_text-SVG3.svg" alt="html tag">
                        </div>
                    </div>
                    <div class="w-10/12">
                        <h2 tabindex="0" class="focus:outline-none text-lg font-semibold leading-tight text-gray-800 dark:text-white">
                            تصميم مواقع مخصص
                        </h2>
                        <p tabindex="0" class="focus:outline-none text-base text-gray-600 dark:text-gray-200 leading-normal pt-2">
                            نقوم بتصميم مواقع فريدة و معدلة حسب احتياجات و متطلبات كل عميل.
                        </p>
                    </div>
                </div>
                <div tabindex="0" aria-label="card 4" class="focus:outline-none flex sm:w-full md:w-5/12 pb-20">
                    <div class="w-20 h-20 relative ml-5">
                        <div class="absolute top-0 right-0 bg-indigo-100 rounded w-16 h-16 mt-2 mr-1"></div>
                        <div class="absolute text-white bottom-0 left-0 bg-indigo-700 rounded w-16 h-16 flex items-center justify-center mt-2 mr-3 hover:scale-110 transition-transform">
                            <img src="https://tuk-cdn.s3.amazonaws.com/can-uploader/icon_and_text-SVG4.svg" alt="mobile">
                        </div>
                    </div>
                    <div class="w-10/12">
                        <h2 tabindex="0" class="focus:outline-none text-lg font-semibold leading-tight text-gray-800 dark:text-white">
                            متجاوب مع جميع الأجهزة
                        </h2>
                        <p tabindex="0" class="focus:outline-none text-base text-gray-600 dark:text-gray-200 leading-normal pt-2">
                            نصمم مواقع متجاوبة مع جميع أحجام الشاشات والأجهزة المختلفة لضمان تجربة مستخدم مثالية
                        </p>
                    </div>
                </div>
                <div tabindex="0" aria-label="card 5" class="focus:outline-none flex sm:w-full md:w-5/12 pb-20">
                    <div class="w-20 h-20 relative ml-5">
                        <div class="absolute top-0 right-0 bg-indigo-100 rounded w-16 h-16 mt-2 mr-1"></div>
                        <div class="absolute text-white bottom-0 left-0 bg-indigo-700 rounded w-16 h-16 flex items-center justify-center mt-2 mr-3 hover:scale-110 transition-transform">
                            <img src="https://tuk-cdn.s3.amazonaws.com/can-uploader/icon_and_text-SVG4.svg" alt="monitor">
                        </div>
                    </div>
                    <div class="w-10/12">
                        <h2 tabindex="0" class="focus:outline-none text-lg font-semibold leading-tight text-gray-800 dark:text-white">
                            الحماية
                        </h2>
                        <p tabindex="0" class="focus:outline-none text-base text-gray-600 dark:text-gray-200 leading-normal pt-2">
                            توفير اعلى وسائل الحماية المتاحة من ناحية الكود البرمجي وامن الخوادم وتفعيل SSL
                        </p>
                    </div>
                </div>
                <div tabindex="0" aria-label="card 6" class="focus:outline-none flex sm:w-full md:w-5/12 pb-20">
                    <div class="w-20 h-20 relative ml-5">
                        <div class="absolute top-0 right-0 bg-indigo-100 rounded w-16 h-16 mt-2 mr-1"></div>
                        <div class="absolute text-white bottom-0 left-0 bg-indigo-700 rounded w-16 h-16 flex items-center justify-center mt-2 mr-3 hover:scale-110 transition-transform">
                            <img src="https://tuk-cdn.s3.amazonaws.com/can-uploader/icon_and_text-SVG3.svg" alt="html tag">
                        </div>
                    </div>
                    <div class="w-10/12">
                        <h2 tabindex="0" class="focus:outline-none text-lg font-semibold leading-tight text-gray-800 dark:text-white">
                            تعدد اللغات
                        </h2>
                        <p tabindex="0" class="focus:outline-none text-base text-gray-600 dark:text-gray-200 leading-normal pt-2">
                            نقدم خيارات لغات عديدة لموقعك فمن الممكن ان يعرض موقعك بلغة واحدة او يعرض باكثر من لغة ويكون حسب اختيارك
                        </p>
                    </div>
                </div>
                <div tabindex="0" aria-label="card 7" class="focus:outline-none flex sm:w-full md:w-5/12 pb-20">
                    <div class="w-20 h-20 relative ml-5">
                        <div class="absolute top-0 right-0 bg-indigo-100 rounded w-16 h-16 mt-2 mr-1"></div>
                        <div class="absolute text-white bottom-0 left-0 bg-indigo-700 rounded w-16 h-16 flex items-center justify-center mt-2 mr-3 hover:scale-110 transition-transform">
                            <img src="https://tuk-cdn.s3.amazonaws.com/can-uploader/icon_and_text-SVG4.svg" alt="monitor">
                        </div>
                    </div>
                    <div class="w-10/12">
                        <h2 tabindex="0" class="focus:outline-none text-lg font-semibold leading-tight text-gray-800 dark:text-white">
                            تصميم سريع
                        </h2>
                        <p tabindex="0" class="focus:outline-none text-base text-gray-600 dark:text-gray-200 leading-normal pt-2">
                            نقدم تصميمات سريعة للتوافق مع محركات البحث لظهور موقعك في محركات البحث بسرعة وجميع المتصفحات
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Website Development Steps Section -->
<section class="text-gray-600 body-font">
    <div role="contentinfo" class="flex items-center flex-col">
        <h1 tabindex="0" class="focus:outline-none text-4xl lg:text-4xl font-extrabold text-center leading-10 text-gray-800 lg:w-5/12 md:w-9/12 pt-10 px-5">
            خطوات تصميم المواقع الإلكترونية
        </h1>
    </div>
    <div class="container py-8 mx-auto flex flex-wrap">
        <div class="flex relative pt-10 pb-20 sm:items-center md:w-2/3 mx-auto">
            <div class="h-full w-6 absolute inset-0 flex items-center justify-center">
                <div class="h-full w-1 bg-gray-200 pointer-events-none"></div>
            </div>
            <div class="flex-shrink-0 w-6 h-6 rounded-full mt-10 sm:mt-0 inline-flex items-center justify-center bg-indigo-500 text-white relative z-10 title-font font-medium text-sm">
                1
            </div>
            <div class="flex-grow md:pl-8 mr-5 pl-6 flex sm:items-center items-start flex-col sm:flex-row">
                <div class="flex-shrink-0 w-24 h-24 hover:scale-105 transition-transform bg-indigo-100 text-indigo-500 rounded-full inline-flex items-center justify-center">
                    <svg fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="w-12 h-12" viewBox="0 0 24 24">
                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                    </svg>
                </div>
                <div class="flex-grow sm:pl-6 mr-5 mt-6 sm:mt-0">
                    <h2 class="font-medium title-font text-gray-900 mb-1 text-xl">التحليل وجمع المعلومات</h2>
                    <p class="leading-relaxed">في هذه المرحلة نقوم بجمع المعلومات عن المشروع بعد الحصول على شرح مفصل من العميل لكافة التفاصيل والوظائف التي يرغب في تواجدها في الموقع الإلكتروني، ويحدد نوع النشاط والغرض منه إن كان خدمي أو تجاري، أو أي غرض آخر مع تقديم أفضل توصيات من جانبنا بأفضل الممارسات التي يمكن إضافتها للموقع.</p>
                </div>
            </div>
        </div>

        <div class="flex relative pb-20 sm:items-center md:w-2/3 mx-auto">
            <div class="h-full w-6 absolute inset-0 flex items-center justify-center">
                <div class="h-full w-1 bg-gray-200 pointer-events-none"></div>
            </div>
            <div class="flex-shrink-0 w-6 h-6 rounded-full mt-10 sm:mt-0 inline-flex items-center justify-center bg-indigo-500 text-white relative z-10 title-font font-medium text-sm">
                2
            </div>
            <div class="flex-grow md:pl-8 mr-5 pl-6 flex sm:items-center items-start flex-col sm:flex-row">
                <div class="flex-shrink-0 w-24 h-24 hover:scale-110 transition-transform bg-indigo-100 text-indigo-500 rounded-full inline-flex items-center justify-center">
                    <svg fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="w-12 h-12" viewBox="0 0 24 24">
                        <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                    </svg>
                </div>
                <div class="flex-grow sm:pl-6 mr-5 mt-6 sm:mt-0">
                    <h2 class="font-medium title-font text-gray-900 mb-1 text-xl">وضع تصميم مبدئي</h2>
                    <p class="leading-relaxed">يقوم فريق التصميم بوضع تصور للتصميم يوضح الشكل المبدئي للموقع وأقسامه المختلفة والألوان المستخدمة والوظائف والعناصر المتاحة به بناء على الهدف من إنشائه والهوية البصرية الخاصة بالعلامة التجارية.</p>
                </div>
            </div>
        </div>

        <div class="flex relative pb-20 sm:items-center md:w-2/3 mx-auto">
            <div class="h-full w-6 absolute inset-0 flex items-center justify-center">
                <div class="h-full w-1 bg-gray-200 pointer-events-none"></div>
            </div>
            <div class="flex-shrink-0 w-6 h-6 rounded-full mt-10 sm:mt-0 inline-flex items-center justify-center bg-indigo-500 text-white relative z-10 title-font font-medium text-sm">
                3
            </div>
            <div class="flex-grow md:pl-8 mr-5 pl-6 flex sm:items-center items-start flex-col sm:flex-row">
                <div class="flex-shrink-0 w-24 h-24 hover:scale-110 transition-transform bg-indigo-100 text-indigo-500 rounded-full inline-flex items-center justify-center">
                    <svg fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="w-12 h-12" viewBox="0 0 24 24">
                        <circle cx="12" cy="5" r="3"></circle>
                        <path d="M12 22V8M5 12H2a10 10 0 0020 0h-3"></path>
                    </svg>
                </div>
                <div class="flex-grow sm:pl-6 mr-5 mt-6 sm:mt-0">
                    <h2 class="font-medium title-font text-gray-900 mb-1 text-xl">التحليل التقني للموقع</h2>
                    <p class="leading-relaxed">بعد الانتهاء من التحليل الأولي مع العميل ووضع تصور مبدئي للموقع بناء عليه، تأتي مرحلة التنفيذ، ووضع الخطط التقنية والآليات الفنية المطلوبة لتنفيذ الموقع.</p>
                </div>
            </div>
        </div>

        <div class="flex relative pb-20 sm:items-center md:w-2/3 mx-auto">
            <div class="h-full w-6 absolute inset-0 flex items-center justify-center">
                <div class="h-full w-1 bg-gray-200 pointer-events-none"></div>
            </div>
            <div class="flex-shrink-0 w-6 h-6 rounded-full mt-10 sm:mt-0 inline-flex items-center justify-center bg-indigo-500 text-white relative z-10 title-font font-medium text-sm">
                4
            </div>
            <div class="flex-grow md:pl-8 mr-5 pl-6 flex sm:items-center items-start flex-col sm:flex-row">
                <div class="flex-shrink-0 w-24 h-24 hover:scale-110 transition-transform bg-indigo-100 text-indigo-500 rounded-full inline-flex items-center justify-center">
                    <svg fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="w-12 h-12" viewBox="0 0 24 24">
                        <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                </div>
                <div class="flex-grow sm:pl-6 mr-5 mt-6 sm:mt-0">
                    <h2 class="font-medium title-font text-gray-900 mb-1 text-xl">تصميم الموقع</h2>
                    <p class="leading-relaxed">يتم العمل على تصميمات UI-UX مع التركيز على اختيار التصميمات والألوان المناسبة، وجاذبية التصميم الملائم لنوع النشاط، مع استخدام أحدث التقنيات في مجال تصميم المواقع</p>
                </div>
            </div>
        </div>

        <div class="flex relative pb-20 sm:items-center md:w-2/3 mx-auto">
            <div class="h-full w-6 absolute inset-0 flex items-center justify-center">
                <div class="h-full w-1 bg-gray-200 pointer-events-none"></div>
            </div>
            <div class="flex-shrink-0 w-6 h-6 rounded-full mt-10 sm:mt-0 inline-flex items-center justify-center bg-indigo-500 text-white relative z-10 title-font font-medium text-sm">
                5
            </div>
            <div class="flex-grow md:pl-8 mr-5 pl-6 flex sm:items-center items-start flex-col sm:flex-row">
                <div class="flex-shrink-0 w-24 h-24 hover:scale-110 transition-transform bg-indigo-100 text-indigo-500 rounded-full inline-flex items-center justify-center">
                    <svg fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="w-12 h-12" viewBox="0 0 24 24">
                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                    </svg>
                </div>
                <div class="flex-grow sm:pl-6 mr-5 mt-6 sm:mt-0">
                    <h2 class="font-medium title-font text-gray-900 mb-1 text-xl">التكويد الداخلي</h2>
                    <p class="leading-relaxed">بعد الانتهاء من التصميم الخارجي للموقع وموافقة العميل عليه، تأتي مرحلة تحويل هذا التصميم بما يضمه من وظائف وأقسام وصور إلى أكواد وهي الصورة المطلوبة لبرمجة الموقع والوصول إلى الشكل النهائي له، مع استخدام أحدث التقنيات ولغات البرمجة المناسبة لتحقيق أفضل النتائج.</p>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
