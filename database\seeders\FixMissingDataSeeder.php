<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FixMissingDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        echo "🔧 إصلاح البيانات المفقودة في جميع الجداول...\n";

        try {
            // 1. إضافة بيانات services
            echo "📝 إضافة بيانات services...\n";
            \DB::table('services')->updateOrInsert(
                ['id' => 1],
                [
                    'name' => 'نظام إدارة المحاسبة',
                    'slug' => 'accounting-management-system',
                    'short_description' => 'نظام محاسبي متكامل لإدارة جميع العمليات المالية',
                    'description' => 'نظام برونكس للمحاسبة يوفر حلول متكاملة لإدارة الحسابات والفواتير والمخزون بطريقة احترافية وآمنة.',
                    'icon' => 'flaticon-computer',
                    'phone' => '*********',
                    'photo' => 'service_1699519555.jpg',
                    'banner' => 'service_banner_1699519555.jpg',
                    'pdf' => '',
                    'seo_title' => 'نظام إدارة المحاسبة',
                    'seo_meta_description' => 'نظام محاسبي متكامل لإدارة جميع العمليات المالية',
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );

            // إضافة المزيد من الخدمات
            for ($i = 2; $i <= 5; $i++) {
                \DB::table('services')->updateOrInsert(
                    ['id' => $i],
                    [
                        'name' => "خدمة رقم $i",
                        'slug' => "service-$i",
                        'short_description' => "وصف قصير للخدمة رقم $i",
                        'description' => "وصف مفصل للخدمة رقم $i",
                        'icon' => 'flaticon-computer',
                        'phone' => '*********',
                        'photo' => 'service_default.jpg',
                        'banner' => 'service_banner_default.jpg',
                        'pdf' => '',
                        'seo_title' => "خدمة رقم $i",
                        'seo_meta_description' => "وصف الخدمة رقم $i",
                        'created_at' => now(),
                        'updated_at' => now()
                    ]
                );
            }
            echo "✅ تم إضافة بيانات services\n";

            // 2. إضافة بيانات other_page_items
            echo "📝 إضافة بيانات other_page_items...\n";
            \DB::table('other_page_items')->updateOrInsert(
                ['id' => 1],
                [
                    'page_about_title' => 'من نحن',
                    'page_about_welcome_status' => 'Show',
                    'page_about_service_heading' => 'خدماتنا',
                    'page_about_service_subheading' => 'ما نقدمه لك',
                    'page_about_service_text' => 'نقدم أفضل الخدمات التقنية',
                    'page_about_service_how_many' => '6',
                    'page_about_service_status' => 'Show',
                    'page_about_team_members_heading' => 'فريق العمل',
                    'page_about_team_members_subheading' => 'تعرف على فريقنا',
                    'page_about_team_members_how_many' => '4',
                    'page_about_team_members_status' => 'Show',
                    'page_team_members_title' => 'فريق العمل',
                    'page_testimonials_title' => 'آراء العملاء',
                    'page_pricing_title' => 'الأسعار',
                    'page_faq_title' => 'الأسئلة الشائعة',
                    'page_services_title' => 'خدماتنا',
                    'page_portfolios_title' => 'أعمالنا',
                    'page_blog_title' => 'المدونة',
                    'page_contact_title' => 'تواصل معنا',
                    'page_contact_send_mail_heading' => 'أرسل رسالة',
                    'page_contact_send_mail_subheading' => 'نحن هنا للمساعدة',
                    'page_contact_info_heading' => 'معلومات التواصل',
                    'page_contact_info_subheading' => 'تواصل معنا',
                    'page_contact_info_text' => 'نحن متاحون للرد على استفساراتك',
                    'page_contact_info_phone_title' => 'الهاتف',
                    'page_contact_info_phone_value' => '*********',
                    'page_contact_info_email_title' => 'البريد الإلكتروني',
                    'page_contact_info_email_value' => '<EMAIL>',
                    'page_contact_info_address_title' => 'العنوان',
                    'page_contact_info_address_value' => 'صنعاء، اليمن',
                    'page_terms_title' => 'شروط الاستخدام',
                    'page_terms_content' => 'محتوى شروط الاستخدام',
                    'page_privacy_title' => 'سياسة الخصوصية',
                    'page_privacy_content' => 'محتوى سياسة الخصوصية',
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
            echo "✅ تم إضافة بيانات other_page_items\n";

            echo "\n🎉 تم إصلاح جميع البيانات المفقودة بنجاح!\n";

        } catch (\Exception $e) {
            echo "❌ خطأ: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
}
