<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RestoreDesignElementsSeeder extends Seeder
{
    public function run(): void
    {
        echo "🎨 استعادة عناصر التصميم المفقودة...\n";
        
        try {
            // 1. تحديث إعدادات الصور في settings لاستخدام الصور الموجودة
            echo "📝 تحديث إعدادات الصور...\n";
            DB::table('settings')->updateOrInsert(
                ['id' => 1],
                [
                    'logo' => 'logo_1745781959.png', // استخدام الصورة الموجودة
                    'logo_dark' => 'logo_1745781959.png',
                    'favicon' => 'favicon_1745789794.png', // استخدام الصورة الموجودة
                    'banner' => 'banner_1704766456.jpg', // استخدام الصورة الموجودة
                    'image_404' => '404_1705309028.jpg',
                    'login_page_photo' => 'login_page_photo_1704942796.jpg',
                    'updated_at' => now()
                ]
            );
            echo "✅ تم تحديث إعدادات الصور\n";
            
            // 2. إضافة slider رئيسي
            echo "📝 إضافة slider رئيسي...\n";
            DB::table('sliders')->updateOrInsert(
                ['id' => 1],
                [
                    'text' => 'نظام برونكس المحاسبي - الحل الأمثل للشركات الصغيرة والمتوسطة في اليمن',
                    'button_text' => 'ابدأ الآن',
                    'button_url' => 'https://app.pronixs.com/register/0/ar',
                    'photo' => 'slider_1699468038.jpg',
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
            echo "✅ تم إضافة slider رئيسي\n";
            
            // 3. تحديث welcome_two_items بالبيانات الأصلية
            echo "📝 تحديث welcome_two_items...\n";
            DB::table('welcome_two_items')->updateOrInsert(
                ['id' => 1],
                [
                    'subheading' => 'Welcome to Agency',
                    'heading' => 'Leading the best digital agency in town',
                    'text' => 'There are many variations of simply free text passages of available but the majority have suffered alteration in some form, by injected hum randomised words which don\'t slightly.',
                    'button_text' => 'Discover More',
                    'button_url' => '#',
                    'experience_year' => '38+',
                    'photo1' => 'welcome_two_photo1_1704850420.jpg',
                    'photo2' => 'welcome_two_photo2_1704850420.jpg',
                    'updated_at' => now()
                ]
            );
            echo "✅ تم تحديث welcome_two_items\n";
            
            // 4. تحديث feature_one_items
            echo "📝 تحديث feature_one_items...\n";
            DB::table('feature_one_items')->updateOrInsert(
                ['id' => 1],
                [
                    'text' => 'بثلاث خطوات تتم في 3 دقائق يكون نظامك جاهز للعمل',
                    'photo' => 'feature_one_photo_1704876660.jpg',
                    'updated_at' => now()
                ]
            );
            echo "✅ تم تحديث feature_one_items\n";
            
            // 5. تحديث feature_two_items
            echo "📝 تحديث feature_two_items...\n";
            DB::table('feature_two_items')->updateOrInsert(
                ['id' => 1],
                [
                    'subheading' => 'Corporate Features',
                    'heading' => 'We Shape the Perfect Solution',
                    'text' => 'There are many variations of passages of available but majority alteration in some form, by humou or randomised words.',
                    'photo' => 'feature_two_photo_1704877679.jpg',
                    'updated_at' => now()
                ]
            );
            echo "✅ تم تحديث feature_two_items\n";
            
            // 6. تحديث why_choose_one_items
            echo "📝 تحديث why_choose_one_items...\n";
            DB::table('why_choose_one_items')->updateOrInsert(
                ['id' => 1],
                [
                    'subheading' => 'القطاعات',
                    'heading' => 'مهما كان نوع مجال عملك برونكس تفهم احتياجاتك',
                    'text' => 'حقق النجاح والنمو الذي تسعى إليه، ساعدنا آلاف الشركات والمؤسسات الصغيرة والمتوسطة على تحقيق أهدافها في كل القطاعات.',
                    'photo' => 'why_choose_one_photo_1704886453.jpg',
                    'updated_at' => now()
                ]
            );
            echo "✅ تم تحديث why_choose_one_items\n";
            
            // 7. تحديث why_choose_two_items
            echo "📝 تحديث why_choose_two_items...\n";
            DB::table('why_choose_two_items')->updateOrInsert(
                ['id' => 1],
                [
                    'subheading' => 'Why choose us',
                    'heading' => 'Building a design easy for business',
                    'photo_over_text' => 'بساطة إلحصول على النظام– الأمر لا يتطلب أي مهارات تقنية.',
                    'photo_over_heading' => 'Top quality marketing solutions',
                    'photo' => 'why_choose_two_photo_1704886719.jpg',
                    'updated_at' => now()
                ]
            );
            echo "✅ تم تحديث why_choose_two_items\n";
            
            // 8. تخطي fun_fact_items لأن الجدول غير موجود
            echo "⚠️ تخطي fun_fact_items (الجدول غير موجود)\n";
            
            echo "\n🎉 تم استعادة جميع عناصر التصميم بنجاح!\n";
            echo "📝 الآن التصميم والتنسيق يجب أن يظهر بشكل صحيح\n";
            
        } catch (\Exception $e) {
            echo "❌ خطأ: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
}
