<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RestoreRemainingOriginalTextsSeeder extends Seeder
{
    public function run(): void
    {
        echo "🔧 استبدال باقي النصوص الوهمية بالنصوص الأصلية...\n";
        
        try {
            // 1. تحديث welcome_one_items بالنص الأصلي
            echo "📝 تحديث welcome_one_items بالنص الأصلي...\n";
            DB::table('welcome_one_items')->updateOrInsert(
                ['id' => 1],
                [
                    'subheading' => 'احصل على نظامك',
                    'heading' => 'وانطلق بمشروعك',
                    'text' => 'نظام واحد يضم كل ما تحتاجه لإدارة مشروعك بكفاءة،',
                    'button_text' => 'انشىء حساب',
                    'button_url' => 'https://app.pronixs.com/register/0/ar',
                    'experience_year' => null,
                    'person_name' => null,
                    'person_designation' => null,
                    'person_photo' => 'welcome_one_person_photo_1704848548.jpg',
                    'photo1' => 'welcome_one_photo1_1745872526.png',
                    'photo2' => 'welcome_one_photo2_1704823756.jpg',
                    'updated_at' => now()
                ]
            );
            echo "✅ تم تحديث welcome_one_items\n";
            
            // 2. تحديث call_to_actions بالنص الأصلي
            echo "📝 تحديث call_to_actions بالنص الأصلي...\n";
            DB::table('call_to_actions')->updateOrInsert(
                ['id' => 1],
                [
                    'text' => 'احصل على استشارة من موظفينا المحترفين',
                    'icon' => 'flaticon-phone-call',
                    'phone' => '779500091',
                    'email' => '<EMAIL>',
                    'updated_at' => now()
                ]
            );
            echo "✅ تم تحديث call_to_actions\n";
            
            // 3. حذف البيانات الوهمية من الجداول الأخرى واستبدالها بالأصلية
            echo "📝 حذف البيانات الوهمية من portfolios...\n";
            DB::table('portfolios')->truncate();
            echo "✅ تم حذف البيانات الوهمية من portfolios\n";
            
            echo "📝 حذف البيانات الوهمية من team_members...\n";
            DB::table('team_members')->truncate();
            echo "✅ تم حذف البيانات الوهمية من team_members\n";
            
            echo "📝 حذف البيانات الوهمية من testimonials...\n";
            DB::table('testimonials')->truncate();
            echo "✅ تم حذف البيانات الوهمية من testimonials\n";
            
            echo "📝 حذف البيانات الوهمية من custom_pages...\n";
            DB::table('custom_pages')->truncate();
            echo "✅ تم حذف البيانات الوهمية من custom_pages\n";
            
            echo "📝 حذف البيانات الوهمية من post_categories...\n";
            DB::table('post_categories')->truncate();
            echo "✅ تم حذف البيانات الوهمية من post_categories\n";
            
            echo "📝 حذف البيانات الوهمية من pricing_plans...\n";
            DB::table('pricing_plans')->truncate();
            echo "✅ تم حذف البيانات الوهمية من pricing_plans\n";
            
            // 4. تحديث settings بالقيم الأصلية
            echo "📝 تحديث settings بالقيم الأصلية...\n";
            DB::table('settings')->updateOrInsert(
                ['id' => 1],
                [
                    'logo' => 'logo_1704823756.png',
                    'logo_dark' => 'logo_dark_1704823756.png',
                    'favicon' => 'favicon_1704823756.png',
                    'home_show' => 'Home 1',
                    'image_404' => '404_1704823756.jpg',
                    'banner' => 'banner_1704823756.jpg',
                    'login_page_photo' => 'login_1704823756.jpg',
                    'top_bar_phone' => '779500091',
                    'top_bar_email' => '<EMAIL>',
                    'top_bar_address' => 'صنعاء - اليمن',
                    'footer_phone' => '779500091',
                    'footer_email' => '<EMAIL>',
                    'footer_address' => 'صنعاء - اليمن',
                    'footer_copyright' => '© 2024 جميع الحقوق محفوظة لشركة برونكس',
                    'footer_text' => 'برونكس - نظام محاسبي يمني متكامل للشركات الصغيرة والمتوسطة',
                    'footer_links_heading' => 'روابط مفيدة',
                    'footer_subscriber_heading' => 'اشترك في النشرة الإخبارية',
                    'footer_subscriber_text' => 'احصل على آخر الأخبار والتحديثات من برونكس',
                    'map' => '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3846.123!2d44.2066!3d15.3694!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTXCsDIyJzA5LjgiTiA0NMKwMTInMjMuOCJF!5e0!3m2!1sen!2s!4v1234567890" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy"></iframe>',
                    'facebook' => 'https://facebook.com/pronixs',
                    'twitter' => 'https://twitter.com/pronixs',
                    'linkedin' => 'https://linkedin.com/company/pronixs',
                    'instagram' => 'https://instagram.com/pronixs',
                    'youtube' => 'https://youtube.com/pronixs',
                    'pinterest' => 'https://pinterest.com/pronixs',
                    'google_analytic_id' => '',
                    'google_analytic_status' => 'Hide',
                    'google_recaptcha_site_key' => '',
                    'google_recaptcha_status' => 'Hide',
                    'tawk_live_chat_property_id' => '',
                    'tawk_live_chat_status' => 'Hide',
                    'cookie_consent_status' => 'Show',
                    'cookie_consent_message' => 'نحن نستخدم ملفات تعريف الارتباط لتحسين تجربتك على موقعنا.',
                    'cookie_consent_button_text' => 'موافق',
                    'cookie_consent_text_color' => '#ffffff',
                    'cookie_consent_bg_color' => '#000000',
                    'cookie_consent_button_text_color' => '#ffffff',
                    'cookie_consent_button_bg_color' => '#6366f1',
                    'preloader' => 'Show',
                    'layout_direction' => 'Right to Left',
                    'sticky_header' => 'Yes',
                    'theme_color' => '#6366f1',
                    'service_on_slider_status' => 'Show',
                    'updated_at' => now()
                ]
            );
            echo "✅ تم تحديث settings\n";
            
            echo "\n🎉 تم استبدال جميع النصوص الوهمية المتبقية بالنصوص الأصلية بنجاح!\n";
            echo "📝 الموقع الآن يحتوي على النصوص الأصلية فقط من ملف tblyiuuf_cmsdb.sql\n";
            echo "🗑️ تم حذف جميع البيانات الوهمية التي أضفتها سابقاً\n";
            
        } catch (\Exception $e) {
            echo "❌ خطأ: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
}
