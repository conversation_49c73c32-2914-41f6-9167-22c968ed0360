<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CompleteOriginalDataSeeder extends Seeder
{
    public function run(): void
    {
        echo "🔧 إدراج البيانات الأصلية الكاملة...\n";
        
        try {
            // 1. تحديث جدول settings
            echo "📝 تحديث جدول settings...\n";
            DB::table('settings')->updateOrInsert(
                ['id' => 1],
                [
                    'logo' => 'logo_1704823756.png',
                    'logo_dark' => 'logo_dark_1704823756.png',
                    'favicon' => 'favicon_1704823756.png',
                    'home_show' => 'Home 1',
                    'image_404' => '404_1704823756.jpg',
                    'banner' => 'banner_1704823756.jpg',
                    'login_page_photo' => 'login_1704823756.jpg',
                    'top_bar_phone' => '779500091',
                    'top_bar_email' => '<EMAIL>',
                    'top_bar_address' => 'صنعاء - اليمن',
                    'footer_phone' => '779500091',
                    'footer_email' => '<EMAIL>',
                    'footer_address' => 'صنعاء - اليمن',
                    'footer_copyright' => '© 2024 جميع الحقوق محفوظة',
                    'footer_text' => 'نحن شركة رائدة في مجال التكنولوجيا',
                    'footer_links_heading' => 'روابط مفيدة',
                    'footer_subscriber_heading' => 'اشترك في النشرة الإخبارية',
                    'footer_subscriber_text' => 'احصل على آخر الأخبار والتحديثات',
                    'map' => '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3846.123!2d44.2066!3d15.3694!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTXCsDIyJzA5LjgiTiA0NMKwMTInMjMuOCJF!5e0!3m2!1sen!2s!4v1234567890" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy"></iframe>',
                    'facebook' => 'https://facebook.com',
                    'twitter' => 'https://twitter.com',
                    'linkedin' => 'https://linkedin.com',
                    'instagram' => 'https://instagram.com',
                    'youtube' => 'https://youtube.com',
                    'pinterest' => 'https://pinterest.com',
                    'google_analytic_id' => '',
                    'google_analytic_status' => 'Hide',
                    'google_recaptcha_site_key' => '',
                    'google_recaptcha_status' => 'Hide',
                    'tawk_live_chat_property_id' => '',
                    'tawk_live_chat_status' => 'Hide',
                    'cookie_consent_status' => 'Show',
                    'cookie_consent_message' => 'نحن نستخدم ملفات تعريف الارتباط لتحسين تجربتك على موقعنا.',
                    'cookie_consent_button_text' => 'موافق',
                    'cookie_consent_text_color' => '#ffffff',
                    'cookie_consent_bg_color' => '#000000',
                    'cookie_consent_button_text_color' => '#ffffff',
                    'cookie_consent_button_bg_color' => '#6366f1',
                    'preloader' => 'Show',
                    'layout_direction' => 'Right to Left',
                    'sticky_header' => 'Yes',
                    'theme_color' => '#6366f1',
                    'service_on_slider_status' => 'Show',
                    'updated_at' => now(),
                    'created_at' => now()
                ]
            );
            echo "✅ تم تحديث جدول settings\n";
            
            // 2. إدراج بيانات video_one_items
            echo "📝 إدراج بيانات video_one_items...\n";
            DB::table('video_one_items')->updateOrInsert(
                ['id' => 1],
                [
                    'heading' => 'Most Trusted Agency',
                    'youtube_video_id' => 'EWEDUrd1i5g',
                    'photo' => 'video_one_photo_1704852598.jpg',
                    'updated_at' => now(),
                    'created_at' => now()
                ]
            );
            echo "✅ تم إدراج بيانات video_one_items\n";
            
            // 3. إدراج بيانات fun_facts
            echo "📝 إدراج بيانات fun_facts...\n";
            DB::table('fun_facts')->updateOrInsert(
                ['id' => 1],
                [
                    'subheading' => 'ارقام تتصاعد كل يوم',
                    'heading' => 'أرقام ملهمة لنجاحات أكبر مع برونكس',
                    'text' => 'وراء كل رقم قصة نجاح جديدة. اكتشف إنجازاتنا التي صنعناها مع روّاد الأعمال الطموحين',
                    'updated_at' => now(),
                    'created_at' => now()
                ]
            );
            echo "✅ تم إدراج بيانات fun_facts\n";
            
            // 4. إدراج بيانات welcome_one_items
            echo "📝 إدراج بيانات welcome_one_items...\n";
            DB::table('welcome_one_items')->updateOrInsert(
                ['id' => 1],
                [
                    'subheading' => 'احصل على نظامك',
                    'heading' => 'وانطلق بمشروعك',
                    'text' => 'نظام واحد يضم كل ما تحتاجه لإدارة مشروعك بكفاءة،',
                    'button_text' => 'انشىء حساب',
                    'button_url' => 'https://app.pronixs.com/register/0/ar',
                    'experience_year' => null,
                    'person_name' => null,
                    'person_designation' => null,
                    'person_photo' => 'welcome_one_person_photo_1704848548.jpg',
                    'photo1' => 'welcome_one_photo1_1745872526.png',
                    'photo2' => 'welcome_one_photo2_1704823756.jpg',
                    'updated_at' => now(),
                    'created_at' => now()
                ]
            );
            echo "✅ تم إدراج بيانات welcome_one_items\n";
            
            // 5. إدراج بيانات call_to_actions
            echo "📝 إدراج بيانات call_to_actions...\n";
            DB::table('call_to_actions')->updateOrInsert(
                ['id' => 1],
                [
                    'text' => 'احصل على استشارة من موظفينا المحترفين',
                    'icon' => 'flaticon-phone-call',
                    'phone' => '779500091',
                    'email' => '<EMAIL>',
                    'updated_at' => now(),
                    'created_at' => now()
                ]
            );
            echo "✅ تم إدراج بيانات call_to_actions\n";
            
            // 6. إدراج بيانات جداول الصفحات الرئيسية
            echo "📝 إدراج بيانات جداول الصفحات الرئيسية...\n";
            $homePageTables = ['home_one_page_items', 'home_two_page_items', 'home_three_page_items', 'home_four_page_items'];
            
            foreach ($homePageTables as $table) {
                DB::table($table)->updateOrInsert(
                    ['id' => 1],
                    [
                        'service_on_slider_status' => 'Show',
                        'service_on_slider_how_many' => '5',
                        'updated_at' => now(),
                        'created_at' => now()
                    ]
                );
                echo "✅ تم إدراج بيانات $table\n";
            }
            
            echo "\n🎉 تم إدراج جميع البيانات الأصلية بنجاح!\n";
            echo "📝 جميع الجداول تحتوي الآن على البيانات المطلوبة\n";
            
        } catch (\Exception $e) {
            echo "❌ خطأ: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
}
